type City {
    id: ID!
    name: String!
    name_original: String
	active: Boolean!
	pioneer: Boolean
	website: String
	un_region: String
	info: Info @schemalessAttribute(source: "info")
	mayor: Mayor @schemalessAttribute(source: "mayor")
	geopol: Geopol @schemalessAttribute(source: "geopol")
	gallery: [Gallery] @schemalessAttribute(source: "gallery")
	activity: [Activity] @schemalessAttribute(source: "activity")
	pivot: CityUser
	users: [User!]! @belongsToMany
	commitment_themes: [CommitmentTheme] @belongsToMany
	cop_themes: [CopTheme] @belongsToMany
	cop_theme: CopTheme @belongsTo
	commitments: [Commitment] @belongsToMany
	commitment_steps: [CommitmentStep] @belongsToMany
	resources: [Resource] @belongsToMany
	commitment_step_targets: [CommitmentStepTarget] @belongsToMany
	mayor_image(collection_name: String @eq): [Media]! @morphMany(relation: "media")
	second_head_image(collection_name: String @eq): [Media]! @morphMany(relation: "media")
	city_image(collection_name: String @eq): [Media]! @morphMany(relation: "media")
	city_logo(collection_name: String @eq): [Media]! @morphMany(relation: "media")
	city_gallery(collection_name: String @eq orderBy: [OrderByClause!] @orderBy): [Media]! @morphMany(relation: "media")
	pathway_resources: [PathwayResource!] @hasMany
	commitment_actions: [CommitmentAction!] @hasMany
	city_image_url_thumb: String
	city_image_url_large: String
	city_logo_url: String
	mayor_image_url: String
	second_head_image_url: String
	commitment_progress: [CommitmentProgressItem]
	commitment_count: Int @count(relation: "commitments")
	resource_count: Int! @count(relation: "resources")
	pathway_resource_count: Int! @count(relation: "pathway_resources")
	report_count: Int! @count(relation: "reports")
	created_at: ReadableDate
	language: String
	nature_pact_items: [NaturePactItem] @belongsToMany
	city_achievement_logos(active: Boolean @scope(name: "active")): [CityLogo] @belongsToMany
}

input OrderByClause{
    field: String!
    order: SortOrder!
}

enum SortOrder {
    ASC
    DESC
}

type Info {
    description: String
    quote: String
    twitter: String
    logo: String
    image: String
    hearabout: String
	twitter: String
	linkedin: String
	facebook: String
	instagram: String
	youtube: String
}

type Mayor {
	mayor_title: String
	mayor_firstname: String
	mayor_lastname: String
	image: String
	second_head_title: String
	second_head_firstname: String
	second_head_lastname: String
	second_head_description: String
}

type Geopol {
	latitude: Float
	longitude: Float
	country: String
	city: String
	population: String
	region: String
}

type Gallery {
	image: String
	desciption: String
}

type Activity {
	step: String
}

type CityUser {
	type: CityUserType
}

type CommitmentProgressItem {
	commitment_id: Int!
	commitment: Commitment
	percentage: Int!
	total_steps: Int!
	completed_steps: Int!
}

type CityCommitmentTheme @model(class: "\\App\\Models\\Pivots\\CityCommitmentTheme") {
	id: ID!
	commitment_theme: CommitmentTheme! @belongsTo
	city_id: Int!
	commitment_theme_id: Int!
	created_at: Date
}

input CreateCityInput {
  	name: String!
	type: CityUserType!
}

input UpdateCityInput {
	id: ID!,
	name: String
	un_region: String
	info: InfoInput
	mayor: MayorInput
	geopol: GeopolInput
	website: String
	uploads: CityUploadsInput
	city_image: [MediaInput]
	city_logo: [MediaInput]
	mayor_image: [MediaInput]
	second_head_image: [MediaInput]
	city_gallery: [MediaInput]
	users: [UserInput]
	language: String
	city_achievement_logos: ConnectBelongsToMany
	cop_themes: ConnectBelongsToMany
}

input UserInput {
	id: Int
	first_name: String
	last_name: String
	title: String
	department: String
	email: String
	contact_number: String
}

input InfoInput {
	description: String
	twitter: String
	quote: String
	twitter: String
	logo: String
	image: String
	hearabout: String
	twitter: String
	linkedin: String
	facebook: String
	instagram: String
	youtube: String
}

input MayorInput {
	mayor_title: String
	mayor_firstname: String
	mayor_lastname: String
	image: String
	second_head_title: String
	second_head_firstname: String
	second_head_lastname: String
	second_head_description: String
}

input GeopolInput {
	country: String
	city: String
	population: Int
	region: String
}

input CityUploadsInput {
	mayor_image: String
	city_image: String
	city_logo: String
	city_gallery: [String]
}

input ConnectBelongsToMany {
  	connect: [ID]
	syncWithoutDetaching: [ID!]
	sync: [ID]
	disconnect: [ID!]
}

input SortByInput {
	column: String
	direction: String
}

extend type Query {
	cities(
		pioneer: Boolean @eq
		name: String @where(operator: "like")
		sortBy: SortByInput @builder(method: "App\\GraphQL\\Builders\\CitiesSortBy")
		byCommitmentTheme: Int @builder(method: "App\\GraphQL\\Builders\\CitiesByCommitmentTheme")
		cop_theme_id: Int @builder(method: "App\\GraphQL\\Builders\\CitiesByCopTheme")
	): [City!]! @paginate(defaultCount: 10 scopes: ["active"]) @orderBy(column: "created_at", direction: DESC)
	city(
		id: ID @eq
		name: String @eq
	): City @find
	cityCommitmentProgress(city_id: ID): [CommitmentProgressItem]
}

extend type Mutation {
	updateCity(
		input: UpdateCityInput @spread
	): City @guard
	connectCityCommitment(
		id: ID!
		commitment_themes: ConnectBelongsToMany
		commitments: ConnectBelongsToMany
		commitment_steps: ConnectBelongsToMany
		commitment_step_targets: ConnectBelongsToMany
	): City @guard @update
	connectCityCopThemes(
		id: ID!
		cop_themes: ConnectBelongsToMany
	): City @update
	connectCityNaturePactItems(
		id: ID!
		nature_pact_items: ConnectBelongsToMany
	): City @update
	connectCityAchievementLogos(
		id: ID!
		city_achievement_logos: ConnectBelongsToMany
	): City @update
}
